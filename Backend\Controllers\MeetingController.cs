using Microsoft.AspNetCore.Mvc;
using AccureMD.TeamsBot.Services;
using AccureMD.TeamsBot.Models;

namespace AccureMD.TeamsBot.Controllers;

[Route("api/meetings")]
[ApiController]
public class MeetingController : ControllerBase
{
    private readonly MeetingService _meetingService;
    private readonly TranscriptionService _transcriptionService;
    private readonly ILogger<MeetingController> _logger;

    public MeetingController(
        MeetingService meetingService, 
        TranscriptionService transcriptionService,
        ILogger<MeetingController> logger)
    {
        _meetingService = meetingService;
        _transcriptionService = transcriptionService;
        _logger = logger;
    }

    [HttpPost("join")]
    public async Task<IActionResult> JoinMeeting([FromBody] MeetingJoinRequest request)
    {
        try
        {
            if (string.IsNullOrEmpty(request.MeetingUrl) || string.IsNullOrEmpty(request.UserId))
            {
                return BadRequest("Meeting URL and User ID are required");
            }

            var result = await _meetingService.JoinMeetingAsGuestAsync(request.MeetingUrl, request.UserId);

            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining meeting");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpPost("{meetingId}/recording/start")]
    public async Task<IActionResult> StartRecording(string meetingId)
    {
        try
        {
            var result = await _meetingService.StartRecordingAsync(meetingId);

            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting recording");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpPost("{meetingId}/recording/stop")]
    public async Task<IActionResult> StopRecording(string meetingId)
    {
        try
        {
            var result = await _meetingService.StopRecordingAsync(meetingId);

            if (result.Success)
            {
                return Ok(result);
            }
            else
            {
                return BadRequest(result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping recording");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpGet("{meetingId}/transcripts")]
    public async Task<IActionResult> GetLiveTranscripts(string meetingId)
    {
        try
        {
            var transcripts = await _transcriptionService.GetLiveTranscriptsAsync(meetingId);
            return Ok(transcripts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting transcripts");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpGet("status")]
    public async Task<IActionResult> GetBotStatus()
    {
        try
        {
            var status = await _meetingService.GetBotStatusAsync();
            return Ok(new { status });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting bot status");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpGet("active/{userId}")]
    public async Task<IActionResult> GetActiveMeetings(string userId)
    {
        try
        {
            var activeMeetings = await _meetingService.GetActiveMeetingsAsync(userId);
            return Ok(activeMeetings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active meetings");
            return StatusCode(500, new { error = ex.Message });
        }
    }

    [HttpPost("{meetingId}/leave")]
    public async Task<IActionResult> LeaveMeeting(string meetingId)
    {
        try
        {
            await _meetingService.LeaveMeetingAsync(meetingId);
            return Ok(new { success = true, message = "Left meeting successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error leaving meeting");
            return StatusCode(500, new { error = ex.Message });
        }
    }
}