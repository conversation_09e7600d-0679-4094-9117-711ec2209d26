{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"AccureMD.TeamsBot/1.0.0": {"dependencies": {"Azure.Storage.Blobs": "12.18.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "8.0.0", "Microsoft.Bot.Builder": "4.21.2", "Microsoft.Bot.Builder.Integration.AspNet.Core": "4.21.2", "Microsoft.CognitiveServices.Speech": "1.34.0", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Graph": "5.32.0", "Microsoft.Identity.Client": "4.74.1", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"AccureMD.TeamsBot.dll": {}}}, "AdaptiveCards/1.2.3": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/AdaptiveCards.dll": {"assemblyVersion": "*******", "fileVersion": "1.2.1909.25002"}}}, "Azure.Core/1.35.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "5.0.1", "System.Text.Json": "8.0.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "********", "fileVersion": "1.3500.23.45706"}}}, "Azure.Storage.Blobs/12.18.0": {"dependencies": {"Azure.Storage.Common": "12.17.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net6.0/Azure.Storage.Blobs.dll": {"assemblyVersion": "12.18.0.0", "fileVersion": "12.1800.23.46203"}}}, "Azure.Storage.Common/12.17.0": {"dependencies": {"Azure.Core": "1.35.0", "System.IO.Hashing": "6.0.0"}, "runtime": {"lib/net6.0/Azure.Storage.Common.dll": {"assemblyVersion": "12.17.0.0", "fileVersion": "12.1700.23.46203"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.0.3"}, "runtime": {"lib/net8.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.20.21406"}}}, "Microsoft.Bot.Builder/4.21.2": {"dependencies": {"Microsoft.Bot.Connector": "4.21.2", "Microsoft.Bot.Connector.Streaming": "4.21.2", "Microsoft.Bot.Streaming": "4.21.2", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Builder.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Builder.Integration.AspNet.Core/4.21.2": {"dependencies": {"Microsoft.Bot.Builder": "4.21.2", "Microsoft.Bot.Configuration": "4.21.2", "Microsoft.Bot.Connector.Streaming": "4.21.2", "Microsoft.Bot.Streaming": "4.21.2", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/net6.0/Microsoft.Bot.Builder.Integration.AspNet.Core.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Configuration/4.21.2": {"dependencies": {"Newtonsoft.Json": "13.0.1", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Configuration.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Connector/4.21.2": {"dependencies": {"Microsoft.Bot.Schema": "4.21.2", "Microsoft.CSharp": "4.5.0", "Microsoft.Extensions.Http": "2.1.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Identity.Client": "4.74.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.0.3", "Microsoft.Rest.ClientRuntime": "2.3.24", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Connector.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Connector.Streaming/4.21.2": {"dependencies": {"Microsoft.Bot.Schema": "4.21.2", "Microsoft.Bot.Streaming": "4.21.2", "Microsoft.Extensions.Logging": "8.0.0", "Newtonsoft.Json": "13.0.1", "System.IO.Pipelines": "5.0.1", "System.Text.Encodings.Web": "5.0.1", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Connector.Streaming.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Schema/4.21.2": {"dependencies": {"AdaptiveCards": "1.2.3", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Schema.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Bot.Streaming/4.21.2": {"dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Net.Http.Headers": "2.1.0", "Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Bot.Streaming.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.CognitiveServices.Speech/1.34.0": {"runtime": {"lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "runtimeTargets": {"runtimes/linux-arm/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "linux-arm", "assetType": "runtime", "assemblyVersion": "*********", "fileVersion": "*********"}, "runtimes/linux-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "linux-arm64", "assetType": "runtime", "assemblyVersion": "*********", "fileVersion": "*********"}, "runtimes/linux-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "linux-x64", "assetType": "runtime", "assemblyVersion": "*********", "fileVersion": "*********"}, "runtimes/osx-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "osx-arm64", "assetType": "runtime", "assemblyVersion": "*********", "fileVersion": "*********"}, "runtimes/osx-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "osx-x64", "assetType": "runtime", "assemblyVersion": "*********", "fileVersion": "*********"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "centos7-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "centos7-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"rid": "centos7-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "centos7-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "centos7-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/centos7-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "centos7-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/ios-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "ios-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/iossimulator-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "iossimulator-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/iossimulator-x64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "iossimulator-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.core.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.core.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.core.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.core.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.core.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.codec.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.core.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.codec.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*********"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.5"}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {}, "Microsoft.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.Extensions.Logging.EventSource": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Http/2.1.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Logging.Console/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Text.Json": "8.0.5"}}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.EventLog": "8.0.0"}}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0", "System.Text.Json": "8.0.5"}}, "Microsoft.Extensions.Options/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives/8.0.0": {}, "Microsoft.Graph/5.32.0": {"dependencies": {"Microsoft.Graph.Core": "3.1.0"}, "runtime": {"lib/net5.0/Microsoft.Graph.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Graph.Core/3.1.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "7.0.3", "Microsoft.Kiota.Abstractions": "1.5.0", "Microsoft.Kiota.Authentication.Azure": "1.1.0", "Microsoft.Kiota.Http.HttpClientLibrary": "1.2.0", "Microsoft.Kiota.Serialization.Form": "1.1.0", "Microsoft.Kiota.Serialization.Json": "1.1.1", "Microsoft.Kiota.Serialization.Multipart": "1.1.0", "Microsoft.Kiota.Serialization.Text": "1.1.0", "NETStandard.Library": "2.0.3", "System.Security.Claims": "4.3.0"}, "runtime": {"lib/net6.0/Microsoft.Graph.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Identity.Client/4.74.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.0.3", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.IdentityModel.Abstractions/7.0.3": {"runtime": {"lib/net8.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.3.41017"}}}, "Microsoft.IdentityModel.JsonWebTokens/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Tokens": "7.0.3"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.3.41017"}}}, "Microsoft.IdentityModel.Logging/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "7.0.3"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.3.41017"}}}, "Microsoft.IdentityModel.Protocols/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.0.3", "Microsoft.IdentityModel.Tokens": "7.0.3"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.3.41017"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Protocols": "7.0.3", "System.IdentityModel.Tokens.Jwt": "7.0.3"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.3.41017"}}}, "Microsoft.IdentityModel.Tokens/7.0.3": {"dependencies": {"Microsoft.IdentityModel.Logging": "7.0.3"}, "runtime": {"lib/net8.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.3.41017"}}}, "Microsoft.Kiota.Abstractions/1.5.0": {"dependencies": {"Std.UriTemplate": "0.0.46", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net5.0/Microsoft.Kiota.Abstractions.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}}}, "Microsoft.Kiota.Authentication.Azure/1.1.0": {"dependencies": {"Azure.Core": "1.35.0", "Microsoft.Kiota.Abstractions": "1.5.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}, "runtime": {"lib/net5.0/Microsoft.Kiota.Authentication.Azure.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Kiota.Http.HttpClientLibrary/1.2.0": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.5.0", "System.Diagnostics.DiagnosticSource": "8.0.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net5.0/Microsoft.Kiota.Http.HttpClientLibrary.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Kiota.Serialization.Form/1.1.0": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.5.0"}, "runtime": {"lib/net5.0/Microsoft.Kiota.Serialization.Form.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Kiota.Serialization.Json/1.1.1": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.5.0", "System.Text.Json": "8.0.5"}, "runtime": {"lib/net5.0/Microsoft.Kiota.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Kiota.Serialization.Multipart/1.1.0": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.5.0"}, "runtime": {"lib/net5.0/Microsoft.Kiota.Serialization.Multipart.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Kiota.Serialization.Text/1.1.0": {"dependencies": {"Microsoft.Kiota.Abstractions": "1.5.0"}, "runtime": {"lib/net5.0/Microsoft.Kiota.Serialization.Text.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Net.Http.Headers/2.1.0": {"dependencies": {"Microsoft.Extensions.Primitives": "8.0.0", "System.Buffers": "4.5.0"}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.Rest.ClientRuntime/2.3.24": {"dependencies": {"Newtonsoft.Json": "13.0.1"}, "runtime": {"lib/netstandard2.0/Microsoft.Rest.ClientRuntime.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.1.25517"}}}, "Std.UriTemplate/0.0.46": {"runtime": {"lib/netstandard2.0/Std.UriTemplate.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "System.Buffers/4.5.0": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/8.0.0": {}, "System.Diagnostics.EventLog/8.0.0": {}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/7.0.3": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "7.0.3", "Microsoft.IdentityModel.Tokens": "7.0.3"}, "runtime": {"lib/net8.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.3.41017"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Hashing/6.0.0": {"runtime": {"lib/net6.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.IO.Pipelines/5.0.1": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "5.0.1", "System.Text.Json": "8.0.5"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encodings.Web/5.0.1": {}, "System.Text.Json/8.0.5": {}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.5.4": {}}}, "libraries": {"AccureMD.TeamsBot/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AdaptiveCards/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-C3lR/uspvCmWmbE9ku4xGD7M5GKjd+1VksQ5HWlu8Lmv99P4tfya3gGArDO99GqGodwK5z9IHmhfemcqk84VCA==", "path": "adaptivecards/1.2.3", "hashPath": "adaptivecards.1.2.3.nupkg.sha512"}, "Azure.Core/1.35.0": {"type": "package", "serviceable": true, "sha512": "sha512-hENcx03Jyuqv05F4RBEPbxz29UrM3Nbhnr6Wl6NQpoU9BCIbL3XLentrxDCTrH54NLS11Exxi/o8MYgT/cnKFA==", "path": "azure.core/1.35.0", "hashPath": "azure.core.1.35.0.nupkg.sha512"}, "Azure.Storage.Blobs/12.18.0": {"type": "package", "serviceable": true, "sha512": "sha512-IUqHRXnabXCzmmvkzqPK4FuGzLxmKSugDEt5Hm5B/JlJFR+aHDsPW4nCLbG0txThqBSKPqcBBU/oA6c5TaFJgA==", "path": "azure.storage.blobs/12.18.0", "hashPath": "azure.storage.blobs.12.18.0.nupkg.sha512"}, "Azure.Storage.Common/12.17.0": {"type": "package", "serviceable": true, "sha512": "sha512-/h8SpUkxMuQy/MbNFeJQGmhYt3JnYfEiGeDojtNgLNzzhyDnRYgjF3ZKYgjORYQpn0Spr+4+v2MZy+0GNJBLrg==", "path": "azure.storage.common/12.17.0", "hashPath": "azure.storage.common.12.17.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-rwxaZYHips5M9vqxRkGfJthTx+Ws4O4yCuefn17J371jL3ouC5Ker43h2hXb5yd9BMnImE9rznT75KJHm6bMgg==", "path": "microsoft.aspnetcore.authentication.jwtbearer/8.0.0", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w==", "path": "microsoft.bcl.asyncinterfaces/1.1.1", "hashPath": "microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512"}, "Microsoft.Bot.Builder/4.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-T9ovZoL3PJylGZeNg6UBwEhrD9agjSYeHrNcRcASaYeje+FI6T4DHa1NMn3ZT8A3skuKvgQah303rZ+KYvfezw==", "path": "microsoft.bot.builder/4.21.2", "hashPath": "microsoft.bot.builder.4.21.2.nupkg.sha512"}, "Microsoft.Bot.Builder.Integration.AspNet.Core/4.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-ZzlPj9BigGxYXZ3xcgjNK7fC11pHsoWCBntrOLNdoAZz2yvbtibWnSoN/PTVfi9XqvDCvzsPLxcyavwYiO48+g==", "path": "microsoft.bot.builder.integration.aspnet.core/4.21.2", "hashPath": "microsoft.bot.builder.integration.aspnet.core.4.21.2.nupkg.sha512"}, "Microsoft.Bot.Configuration/4.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-pzXVCUg2ixQNioU33CpEF7pUzbjEs44Y4RB5M/XzCEvU7QbMupaMlPijFeCzZpg4y5KZZoBaHRKAWO9acDBPXg==", "path": "microsoft.bot.configuration/4.21.2", "hashPath": "microsoft.bot.configuration.4.21.2.nupkg.sha512"}, "Microsoft.Bot.Connector/4.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-tOOb/gyfI28Vsof7P9QrnJo62okkcCxHXgrA7BuUpjazEyWdPfaRTQOgXUBqqgnHGQrJRGe8I98JFQMr/hBFmA==", "path": "microsoft.bot.connector/4.21.2", "hashPath": "microsoft.bot.connector.4.21.2.nupkg.sha512"}, "Microsoft.Bot.Connector.Streaming/4.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-dhxrPhBORX2U60dq2EmbjVSwwoEcKgcFGUznlr6NaizYZKER1q4JqZ5BXOCg2HsTwqe7uB3YOx8fkWt+mXE5jA==", "path": "microsoft.bot.connector.streaming/4.21.2", "hashPath": "microsoft.bot.connector.streaming.4.21.2.nupkg.sha512"}, "Microsoft.Bot.Schema/4.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-OkJQOwhYPiHpRhoehzQGHVh5lQmzQb636K+ZWmDTJluNcrFBMkvEj0WWc7rDX+caWD7+OrqhYt+i/iofbjZnqg==", "path": "microsoft.bot.schema/4.21.2", "hashPath": "microsoft.bot.schema.4.21.2.nupkg.sha512"}, "Microsoft.Bot.Streaming/4.21.2": {"type": "package", "serviceable": true, "sha512": "sha512-4IAMvO6SN/nLaHVdB2IlC538+eM59L6st62ouVO20peo9Hdkol5PIIw7fW2gOXXWL32aZ//8LhYmHzFbzKF/gA==", "path": "microsoft.bot.streaming/4.21.2", "hashPath": "microsoft.bot.streaming.4.21.2.nupkg.sha512"}, "Microsoft.CognitiveServices.Speech/1.34.0": {"type": "package", "serviceable": true, "sha512": "sha512-AnsDqBc6DJ7dkmLMH1RsduuJzIT8rm9Y5WNP5T1Fl5V+Zo81g7HsVdVybnlBp3Y8H3asWYxRSfR8yOeW8lDx0g==", "path": "microsoft.cognitiveservices.speech/1.34.0", "hashPath": "microsoft.cognitiveservices.speech.1.34.0.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "path": "microsoft.extensions.configuration.commandline/8.0.0", "hashPath": "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "path": "microsoft.extensions.dependencyinjection/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ItYHpdqVp5/oFLT5QqbopnkKlyFG9EW/9nhM6/yfObeKt6Su0wkBio6AizgRHGNwhJuAtlE5VIjow5JOTrip6w==", "path": "microsoft.extensions.hosting/8.0.0", "hashPath": "microsoft.extensions.hosting.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-vkSkGa1UIZVlAd18oDhrtoawN/q7fDemJcVpT9+28mP7bP0I8zKLSLRwqF++GmPs/7e0Aqlo6jpZm3P7YYS0ag==", "path": "microsoft.extensions.http/2.1.0", "hashPath": "microsoft.extensions.http.2.1.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "path": "microsoft.extensions.logging/8.0.0", "hashPath": "microsoft.extensions.logging.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "path": "microsoft.extensions.logging.abstractions/8.0.0", "hashPath": "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ixXXV0G/12g6MXK65TLngYN9V5hQQRuV+fZi882WIoVJT7h5JvoYoxTEwCgdqwLjSneqh1O+66gM8sMr9z/rsQ==", "path": "microsoft.extensions.logging.configuration/8.0.0", "hashPath": "microsoft.extensions.logging.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e+48o7DztoYog+PY430lPxrM4mm3PbA6qucvQtUDDwVo4MO+ejMw7YGc/o2rnxbxj4isPxdfKFzTxvXMwAz83A==", "path": "microsoft.extensions.logging.console/8.0.0", "hashPath": "microsoft.extensions.logging.console.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "path": "microsoft.extensions.logging.debug/8.0.0", "hashPath": "microsoft.extensions.logging.debug.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3X9D3sl7EmOu7vQp5MJrmIJBl5XSdOhZPYXUeFfYa6Nnm9+tok8x3t3IVPLhm7UJtPOU61ohFchw8rNm9tIYOQ==", "path": "microsoft.extensions.logging.eventlog/8.0.0", "hashPath": "microsoft.extensions.logging.eventlog.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oKcPMrw+luz2DUAKhwFXrmFikZWnyc8l2RKoQwqU3KIZZjcfoJE0zRHAnqATfhRZhtcbjl/QkiY2Xjxp0xu+6w==", "path": "microsoft.extensions.logging.eventsource/8.0.0", "hashPath": "microsoft.extensions.logging.eventsource.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "path": "microsoft.extensions.options/8.0.0", "hashPath": "microsoft.extensions.options.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "path": "microsoft.extensions.primitives/8.0.0", "hashPath": "microsoft.extensions.primitives.8.0.0.nupkg.sha512"}, "Microsoft.Graph/5.32.0": {"type": "package", "serviceable": true, "sha512": "sha512-U9q/FsePoLwHcSdVN5Vo5mqnkhZAA8KKCRuN4/8CXauS8yvz567xiqDxcWXN3grW+4kKX1nCV6sS/iWYmRtoCw==", "path": "microsoft.graph/5.32.0", "hashPath": "microsoft.graph.5.32.0.nupkg.sha512"}, "Microsoft.Graph.Core/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-I23RHiHAIJwTOW6abhtld2LyP3WRo5gru2KG9rjlN+Tg1o9J9tkrexvb2xAblc+9927JM5HKEfmdeUR4/ailSQ==", "path": "microsoft.graph.core/3.1.0", "hashPath": "microsoft.graph.core.3.1.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.74.1": {"type": "package", "serviceable": true, "sha512": "sha512-OJbHQ26k5TkvHdIss6WYjI1MyJQpi1dF8PivqgSmUIllHQAWNBnV3jg0QNfZAiZbGgwIH8f8Th+CfVQT0gGRMg==", "path": "microsoft.identity.client/4.74.1", "hashPath": "microsoft.identity.client.4.74.1.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-cfPUWdjigLIRIJSKz3uaZxShgf86RVDXHC1VEEchj1gnY25akwPYpbrfSoIGDCqA9UmOMdlctq411+2pAViFow==", "path": "microsoft.identitymodel.abstractions/7.0.3", "hashPath": "microsoft.identitymodel.abstractions.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-vxjHVZbMKD3rVdbvKhzAW+7UiFrYToUVm3AGmYfKSOAwyhdLl/ELX1KZr+FaLyyS5VReIzWRWJfbOuHM9i6ywg==", "path": "microsoft.identitymodel.jsonwebtokens/7.0.3", "hashPath": "microsoft.identitymodel.jsonwebtokens.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-b6GbGO+2LOTBEccHhqoJsOsmemG4A/MY+8H0wK/ewRhiG+DCYwEnucog1cSArPIY55zcn+XdZl0YEiUHkpDISQ==", "path": "microsoft.identitymodel.logging/7.0.3", "hashPath": "microsoft.identitymodel.logging.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-BtwR+tctBYhPNygyZmt1Rnw74GFrJteW+1zcdIgyvBCjkek6cNwPPqRfdhzCv61i+lwyNomRi8+iI4QKd4YCKA==", "path": "microsoft.identitymodel.protocols/7.0.3", "hashPath": "microsoft.identitymodel.protocols.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-W97TraHApDNArLwpPcXfD+FZH7njJsfEwZE9y9BoofeXMS8H0LBBobz0VOmYmMK4mLdOKxzN7SFT3Ekg0FWI3Q==", "path": "microsoft.identitymodel.protocols.openidconnect/7.0.3", "hashPath": "microsoft.identitymodel.protocols.openidconnect.7.0.3.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-wB+LlbDjhnJ98DULjmFepqf9eEMh/sDs6S6hFh68iNRHmwollwhxk+nbSSfpA5+j+FbRyNskoaY4JsY1iCOKCg==", "path": "microsoft.identitymodel.tokens/7.0.3", "hashPath": "microsoft.identitymodel.tokens.7.0.3.nupkg.sha512"}, "Microsoft.Kiota.Abstractions/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-oBG0dRoYDPJcu+DpeQmKmoFIchJuBOAfVq5a1UuwhvpQajwA+PPuxAjKJ3XPk/Rapuna9Ru030TS4maOcNGpGg==", "path": "microsoft.kiota.abstractions/1.5.0", "hashPath": "microsoft.kiota.abstractions.1.5.0.nupkg.sha512"}, "Microsoft.Kiota.Authentication.Azure/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-NDHhWcneWsTvvqWNDzFu9VcXyGus7dXAJW15Uq1EDIBWFHv2PGXSyS6K30Sje9I3ajnR5+MKRMl6/I8GizMDrQ==", "path": "microsoft.kiota.authentication.azure/1.1.0", "hashPath": "microsoft.kiota.authentication.azure.1.1.0.nupkg.sha512"}, "Microsoft.Kiota.Http.HttpClientLibrary/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-cCZjgXWRT/yfUX6rct1bStht6HC1tyHUpGf9OwPN+JT2ZegGwZ7UomUHIHWLUAFsQNr7zSI/WwyH1DZlds2mcw==", "path": "microsoft.kiota.http.httpclientlibrary/1.2.0", "hashPath": "microsoft.kiota.http.httpclientlibrary.1.2.0.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Form/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-AEm+JgyBC1S4Ec7cdvPmMQNvqAncFukHUAKbzrkKcpJYrkZHDS5VpUeyLD1pUvMvwPwcu/+A4J1fh2d3UFm3BA==", "path": "microsoft.kiota.serialization.form/1.1.0", "hashPath": "microsoft.kiota.serialization.form.1.1.0.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Json/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-jEc2PESzpl8EgWhJENgnoxYdpyl9Bpz7kZ8KcQkKkrE8ti6Lrl2sSf2xYOEzsIifKFYcaFHxhCd9swaX/iyy4w==", "path": "microsoft.kiota.serialization.json/1.1.1", "hashPath": "microsoft.kiota.serialization.json.1.1.1.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Multipart/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-IwNqzImI0mTYRwgIxJd94W21QkUtuaoM3pbs3U/0MgM8NlQJbD4qymmfR89qwmggkVz5yuLtuX+/m/J3XRaBYg==", "path": "microsoft.kiota.serialization.multipart/1.1.0", "hashPath": "microsoft.kiota.serialization.multipart.1.1.0.nupkg.sha512"}, "Microsoft.Kiota.Serialization.Text/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-yVD1hQ6vwAD63kaL7b7Sa6z+jTmsUiQSPJHsn4h8R7hkAXnMrm1hXo6u5f5hcLGZV30mtsGYlMm4nwWDCh5LZQ==", "path": "microsoft.kiota.serialization.text/1.1.0", "hashPath": "microsoft.kiota.serialization.text.1.1.0.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-c08F7C7BGgmjrq9cr7382pBRhcimBx24YOv4M4gtzMIuVKmxGoRr5r9A2Hke9v7Nx7zKKCysk6XpuZasZX4oeg==", "path": "microsoft.net.http.headers/2.1.0", "hashPath": "microsoft.net.http.headers.2.1.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.Rest.ClientRuntime/2.3.24": {"type": "package", "serviceable": true, "sha512": "sha512-hZH7XgM3eV2jFrnq7Yf0nBD4WVXQzDrer2gEY7HMNiwio2hwDsTHO6LWuueNQAfRpNp4W7mKxcXpwXUiuVIlYw==", "path": "microsoft.rest.clientruntime/2.3.24", "hashPath": "microsoft.rest.clientruntime.2.3.24.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "Std.UriTemplate/0.0.46": {"type": "package", "serviceable": true, "sha512": "sha512-/cCCMsB3i+MVt5LTbl236dnFd/BE4dKzzzC1teGTpAHzwPTiLIuD5hioGgtPuli/enAj8Dhmt/e9JlVUIITIgQ==", "path": "std.uritemplate/0.0.46", "hashPath": "std.uritemplate.0.0.46.nupkg.sha512"}, "System.Buffers/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-pL2ChpaRRWI/p4LXyy4RgeWlYF2sgfj/pnVMvBqwNFr5cXg7CXNnWZWxrOONLg8VGdFB8oB+EG2Qw4MLgTOe+A==", "path": "system.buffers/4.5.0", "hashPath": "system.buffers.4.5.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ==", "path": "system.diagnostics.diagnosticsource/8.0.0", "hashPath": "system.diagnostics.diagnosticsource.8.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "path": "system.diagnostics.eventlog/8.0.0", "hashPath": "system.diagnostics.eventlog.8.0.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/7.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-caEe+OpQNYNiyZb+DJpUVROXoVySWBahko2ooNfUcllxa9ZQUM8CgM/mDjP6AoFn6cQU9xMmG+jivXWub8cbGg==", "path": "system.identitymodel.tokens.jwt/7.0.3", "hashPath": "system.identitymodel.tokens.jwt.7.0.3.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Hashing/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfm2jYCaUeGysFEZjDe7j1R4x6Z6BzumS/vUT5a1AA/AWJuGX71PoGB0RmpyX3VmrGqVnAwtfMn39OHR8Y/5+g==", "path": "system.io.hashing/6.0.0", "hashPath": "system.io.hashing.6.0.0.nupkg.sha512"}, "System.IO.Pipelines/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qEePWsaq9LoEEIqhbGe6D5J8c9IqQOUuTzzV6wn1POlfdLkJliZY3OlB0j0f17uMWlqZYjH7txj+2YbyrIA8Yg==", "path": "system.io.pipelines/5.0.1", "hashPath": "system.io.pipelines.5.0.1.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Security.Claims/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "path": "system.security.claims/4.3.0", "hashPath": "system.security.claims.4.3.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KmJ+CJXizDofbq6mpqDoRRLcxgOd2z9X3XoFNULSbvbqVRZkFX3istvr+MUjL6Zw1RT+RNdoI4GYidIINtgvqQ==", "path": "system.text.encodings.web/5.0.1", "hashPath": "system.text.encodings.web.5.0.1.nupkg.sha512"}, "System.Text.Json/8.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-0f1B50Ss7rqxXiaBJyzUu9bWFOO2/zSlifZ/UNMdiIpDYe4cY4LQQicP4nirK1OS31I43rn062UIJ1Q9bpmHpg==", "path": "system.text.json/8.0.5", "hashPath": "system.text.json.8.0.5.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}}}