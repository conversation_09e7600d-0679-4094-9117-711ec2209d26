<!DOCTYPE html>
<html>
<head>
    <title>Test Teams Authentication Redirect</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #0078d4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #106ebe;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Teams Authentication Redirect Test</h1>
        <p>This page tests the new <code>/api/auth/start-teams-login</code> endpoint that performs server-side redirects for Teams authentication.</p>
        
        <div class="test-section info">
            <h3>Test Parameters</h3>
            <p><strong>User ID:</strong> <span id="userId">test-user-123</span></p>
            <p><strong>Redirect URI:</strong> <span id="redirectUri"></span></p>
            <p><strong>Teams Login URL:</strong> <span id="teamsLoginUrl"></span></p>
        </div>

        <div class="test-section">
            <h3>Test 1: JSON Response (Original Endpoint)</h3>
            <p>Test the original <code>/api/auth/login</code> endpoint that returns JSON with auth URL.</p>
            <button onclick="testJsonEndpoint()">Test JSON Endpoint</button>
            <div id="jsonResult"></div>
        </div>

        <div class="test-section">
            <h3>Test 2: Redirect Response (New Teams Endpoint)</h3>
            <p>Test the new <code>/api/auth/start-teams-login</code> endpoint that performs HTTP 302 redirect.</p>
            <button onclick="testRedirectEndpoint()">Test Redirect Endpoint</button>
            <div id="redirectResult"></div>
        </div>

        <div class="test-section">
            <h3>Test 3: Teams Authentication Flow</h3>
            <p>Test the complete Teams authentication flow using the new endpoint.</p>
            <button onclick="testTeamsFlow()">Test Teams Flow</button>
            <div id="teamsResult"></div>
        </div>
    </div>

    <script>
        // Initialize test parameters
        const userId = 'test-user-123';
        const redirectUri = window.location.origin + '/html/auth-callback.html';
        const teamsLoginUrl = `${window.location.origin}/api/auth/start-teams-login?userId=${encodeURIComponent(userId)}&redirectUri=${encodeURIComponent(redirectUri)}`;

        document.getElementById('userId').textContent = userId;
        document.getElementById('redirectUri').textContent = redirectUri;
        document.getElementById('teamsLoginUrl').textContent = teamsLoginUrl;

        async function testJsonEndpoint() {
            const resultDiv = document.getElementById('jsonResult');
            resultDiv.innerHTML = '<p>Testing JSON endpoint...</p>';

            try {
                const url = `/api/auth/login?userId=${encodeURIComponent(userId)}&redirectUri=${encodeURIComponent(redirectUri)}`;
                console.log('Testing JSON endpoint:', url);

                const response = await fetch(url);
                console.log('JSON endpoint response status:', response.status);

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                console.log('JSON endpoint response data:', data);

                resultDiv.innerHTML = `
                    <div class="success">
                        <h4>✅ JSON Endpoint Test Successful</h4>
                        <p><strong>Status:</strong> ${response.status}</p>
                        <p><strong>Success:</strong> ${data.success}</p>
                        <p><strong>Message:</strong> ${data.message}</p>
                        <p><strong>Auth URL:</strong> <a href="${data.authUrl}" target="_blank">Open Auth URL</a></p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                console.error('JSON endpoint test failed:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ JSON Endpoint Test Failed</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        async function testRedirectEndpoint() {
            const resultDiv = document.getElementById('redirectResult');
            resultDiv.innerHTML = '<p>Testing redirect endpoint...</p>';

            try {
                const url = teamsLoginUrl;
                console.log('Testing redirect endpoint:', url);

                // Use fetch with redirect: 'manual' to capture the redirect response
                const response = await fetch(url, { redirect: 'manual' });
                console.log('Redirect endpoint response status:', response.status);

                if (response.status === 302 || response.status === 301) {
                    const location = response.headers.get('Location');
                    console.log('Redirect location:', location);

                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Redirect Endpoint Test Successful</h4>
                            <p><strong>Status:</strong> ${response.status} (Redirect)</p>
                            <p><strong>Location:</strong> <a href="${location}" target="_blank">Open Redirect URL</a></p>
                            <p>The endpoint correctly performs a server-side redirect to Microsoft login.</p>
                        </div>
                    `;
                } else {
                    throw new Error(`Expected redirect (302), got ${response.status}`);
                }
            } catch (error) {
                console.error('Redirect endpoint test failed:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Redirect Endpoint Test Failed</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }

        function testTeamsFlow() {
            const resultDiv = document.getElementById('teamsResult');
            
            // Check if we're in Teams context
            if (typeof microsoftTeams === 'undefined') {
                resultDiv.innerHTML = `
                    <div class="info">
                        <h4>ℹ️ Teams SDK Not Available</h4>
                        <p>This test requires the Microsoft Teams SDK. To test the complete flow:</p>
                        <ol>
                            <li>Open this page in Microsoft Teams</li>
                            <li>Or manually test by opening: <a href="${teamsLoginUrl}" target="_blank">${teamsLoginUrl}</a></li>
                        </ol>
                    </div>
                `;
                return;
            }

            resultDiv.innerHTML = '<p>Starting Teams authentication flow...</p>';

            try {
                microsoftTeams.authentication.authenticate({
                    url: teamsLoginUrl,
                    width: 600,
                    height: 700,
                    successCallback: (result) => {
                        console.log('Teams auth flow succeeded:', result);
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h4>✅ Teams Authentication Flow Successful</h4>
                                <p>Authentication completed successfully!</p>
                                <pre>${JSON.stringify(result, null, 2)}</pre>
                            </div>
                        `;
                    },
                    failureCallback: (reason) => {
                        console.error('Teams auth flow failed:', reason);
                        resultDiv.innerHTML = `
                            <div class="error">
                                <h4>❌ Teams Authentication Flow Failed</h4>
                                <p><strong>Reason:</strong> ${reason}</p>
                            </div>
                        `;
                    }
                });
            } catch (error) {
                console.error('Teams flow test failed:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>❌ Teams Flow Test Failed</h4>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
