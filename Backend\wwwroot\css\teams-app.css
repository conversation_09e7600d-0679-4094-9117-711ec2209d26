/* AccureMD Teams App Styles */

:root {
    --teams-primary: #6264A7;
    --teams-secondary: #464775;
    --teams-accent: #A4A3D1;
    --teams-background: #F8F8F8;
    --teams-surface: #FFFFFF;
    --teams-text: #252423;
    --teams-text-secondary: #605E5C;
    --success-color: #107C10;
    --warning-color: #FF8C00;
    --error-color: #D13438;
    --recording-color: #C50E20;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--teams-background);
    color: var(--teams-text);
    line-height: 1.5;
}

.loading-screen {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background-color: var(--teams-surface);
}

.loader {
    width: 40px;
    height: 40px;
    border: 4px solid var(--teams-accent);
    border-top: 4px solid var(--teams-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.auth-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background-color: var(--teams-surface);
}

.auth-container {
    text-align: center;
    padding: 32px;
    max-width: 400px;
}

.logo-large {
    width: 64px;
    height: 64px;
    margin-bottom: 24px;
}

.auth-container h1 {
    font-size: 24px;
    margin-bottom: 8px;
    color: var(--teams-primary);
}

.auth-container p {
    color: var(--teams-text-secondary);
    margin-bottom: 32px;
    font-size: 14px;
}

.login-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--teams-primary);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: background-color 0.3s ease;
    width: 100%;
}

.login-btn:hover {
    background-color: var(--teams-secondary);
}

.ms-icon {
    width: 16px;
    height: 16px;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMSIgaGVpZ2h0PSIyMSI+PHBhdGggZD0iTTAgMGgxMHYxMEgweiIgZmlsbD0iI2Y5ZjlmOSIvPjxwYXRoIGQ9Ik0xMSAwaDEwdjEwSDExeiIgZmlsbD0iI2Y5ZjlmOSIvPjxwYXRoIGQ9Ik0wIDExaDEwdjEwSDB6IiBmaWxsPSIjZjlmOWY5Ii8+PHBhdGggZD0iTTExIDExaDEwdjEwSDExeiIgZmlsbD0iI2Y5ZjlmOSIvPjwvc3ZnPg==') no-repeat center;
    margin-right: 8px;
}

.main-app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.header {
    background-color: var(--teams-surface);
    border-bottom: 1px solid #E1DFDD;
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--teams-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 12px;
}

.logout-btn {
    background: none;
    border: 1px solid var(--teams-primary);
    color: var(--teams-primary);
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background-color: var(--teams-primary);
    color: white;
}

.content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.meeting-controls, .recording-controls, .transcript-panel {
    background-color: var(--teams-surface);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.meeting-controls h2, .recording-controls h2, .transcript-panel h2 {
    font-size: 16px;
    margin-bottom: 16px;
    color: var(--teams-primary);
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.input-group label {
    font-weight: 600;
    font-size: 14px;
    color: var(--teams-text);
}

.input-group input[type="url"] {
    padding: 8px 12px;
    border: 1px solid #C8C6C4;
    border-radius: 4px;
    font-size: 14px;
    width: 100%;
}

.input-group input[type="url"]:focus {
    outline: none;
    border-color: var(--teams-primary);
    box-shadow: 0 0 0 1px var(--teams-primary);
}

.primary-btn, .record-btn, .stop-btn, .transcribe-btn {
    background-color: var(--teams-primary);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: background-color 0.3s ease;
    margin-top: 8px;
}

.primary-btn:hover {
    background-color: var(--teams-secondary);
}

.record-btn {
    background-color: var(--recording-color);
}

.record-btn:hover {
    background-color: #A01017;
}

.stop-btn {
    background-color: var(--teams-text-secondary);
}

.stop-btn:hover {
    background-color: #323130;
}

.transcribe-btn {
    background-color: var(--success-color);
}

.transcribe-btn:hover {
    background-color: #0B5A0B;
}

.secondary-btn {
    background-color: transparent;
    color: var(--teams-primary);
    border: 1px solid var(--teams-primary);
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.secondary-btn:hover {
    background-color: var(--teams-primary);
    color: white;
}

.status-display {
    margin-top: 16px;
    padding: 12px;
    background-color: var(--teams-background);
    border-radius: 4px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-dot.offline {
    background-color: var(--teams-text-secondary);
}

.status-dot.online {
    background-color: var(--success-color);
}

.status-dot.recording {
    background-color: var(--recording-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.control-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.recording-status {
    margin-top: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background-color: var(--teams-background);
    border-radius: 4px;
}

.timer {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    font-weight: 600;
    color: var(--teams-text);
}

.recording-indicator {
    font-size: 14px;
    font-weight: 600;
}

.transcript-container {
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #E1DFDD;
    border-radius: 4px;
    padding: 16px;
    background-color: var(--teams-background);
}

.transcript-entry {
    margin-bottom: 12px;
    padding: 8px;
    background-color: var(--teams-surface);
    border-radius: 4px;
    border-left: 3px solid var(--teams-primary);
}

.transcript-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;
    color: var(--teams-text-secondary);
}

.speaker-name {
    font-weight: 600;
    color: var(--teams-primary);
}

.timestamp {
    font-family: 'Courier New', monospace;
}

.transcript-text {
    font-size: 14px;
    line-height: 1.4;
}

.confidence-score {
    font-size: 11px;
    color: var(--teams-text-secondary);
    margin-top: 4px;
}

.no-transcript {
    text-align: center;
    color: var(--teams-text-secondary);
    font-style: italic;
    padding: 40px 20px;
}

.transcript-controls {
    margin-top: 12px;
    display: flex;
    gap: 8px;
}

.footer {
    background-color: var(--teams-surface);
    border-top: 1px solid #E1DFDD;
    padding: 12px 20px;
    text-align: center;
    color: var(--teams-text-secondary);
    font-size: 12px;
}

/* Configure page styles */
.configure-page {
    background-color: var(--teams-surface);
    padding: 20px;
}

.configure-page .container {
    max-width: 600px;
    margin: 0 auto;
}

.configure-page .header {
    text-align: center;
    margin-bottom: 32px;
    border-bottom: none;
    padding-bottom: 0;
}

.logo {
    width: 32px;
    height: 32px;
    margin-bottom: 16px;
}

.configure-page h1 {
    font-size: 24px;
    color: var(--teams-primary);
    margin-bottom: 8px;
}

.configure-page p {
    color: var(--teams-text-secondary);
}

.config-form {
    background-color: var(--teams-background);
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: var(--teams-text);
}

.form-group input[type="text"],
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #C8C6C4;
    border-radius: 4px;
    font-size: 14px;
}

.form-group input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.2);
}

.actions {
    text-align: center;
}

.actions button {
    margin: 0 8px;
    min-width: 120px;
}

/* Responsive design */
@media (max-width: 768px) {
    .content {
        padding: 16px;
    }

    .control-buttons {
        flex-direction: column;
    }

    .recording-status {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .transcript-controls {
        flex-direction: column;
    }
}

/* Notification System */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.notification {
    animation: slideIn 0.3s ease-out;
    transition: all 0.3s ease;
}

.notification:hover {
    opacity: 0.9 !important;
    transform: scale(1.02);
}

.notification.dismissing {
    animation: slideOut 0.3s ease-in;
}

/* Enhanced status indicators */
.status-dot.connecting {
    background-color: var(--warning-color);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

/* Meeting selection styles */
.meeting-selection {
    background: var(--teams-surface);
    border: 2px solid var(--teams-accent);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.meeting-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.meeting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: var(--teams-background);
    border-radius: 6px;
    border: 1px solid var(--teams-accent);
}

.meeting-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.meeting-info strong {
    color: var(--teams-text);
    font-weight: 600;
}

.meeting-info small {
    color: var(--teams-text-secondary);
    font-size: 12px;
}

.connect-btn {
    background: var(--teams-primary);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.2s;
}

.connect-btn:hover {
    background: var(--teams-secondary);
}