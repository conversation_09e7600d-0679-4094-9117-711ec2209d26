<!DOCTYPE html>
<html>
<head>
    <title>AccureMD - Authentication Start</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://res.cdn.office.net/teams-js/2.31.0/js/MicrosoftTeams.min.js"></script>
</head>
<body>
    <div style="text-align: center; padding: 20px;">
        <h2>Redirecting to Microsoft Sign-in...</h2>
        <p>Please wait while we redirect you to Microsoft for authentication.</p>
        <div style="margin: 20px;">
            <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
        </div>
    </div>

    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
    </style>

    <script>
        console.log('AccureMD: Auth start page loaded');

        // Helper function to generate GUID
        function generateGuid() {
            return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
                var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
                return v.toString(16);
            });
        }

        // Helper function to convert object to query string
        function toQueryString(obj) {
            return Object.keys(obj).map(key => encodeURIComponent(key) + '=' + encodeURIComponent(obj[key])).join('&');
        }

        // Enhanced authentication start with better error handling
        async function startAuthentication() {
            try {
                console.log('AccureMD: Starting authentication process...');

                // Determine state and tenant/loginHint using Teams context when available
                let tenantId = 'common';
                let loginHint = '';
                let state = generateGuid(); // default

                // Try to get Teams context if available
                try {
                    if (typeof microsoftTeams !== 'undefined') {
                        await microsoftTeams.app.initialize();
                        console.log('AccureMD: Teams SDK initialized in auth start page');

                        const context = await microsoftTeams.app.getContext();
                        console.log('AccureMD: Got Teams context:', context);

                        // Use Teams context if available
                        tenantId = context.user?.tenant?.id || 'common';
                        loginHint = context.user?.loginHint || context.user?.userPrincipalName || '';
                        // Prefer stable user id as state so backend can map session by user
                        if (context.user?.id) {
                            state = context.user.id;
                        }
                    }
                } catch (teamsError) {
                    console.warn('AccureMD: Teams context not available, using defaults:', teamsError);
                    // Continue with defaults
                }

                // Persist state for validation
                localStorage.setItem('accuremd.auth.state', state);
                localStorage.removeItem('accuremd.auth.error');
                console.log('AccureMD: Using state value:', state);

                // Build OAuth URL parameters with proper scopes
                const queryParams = {
                    client_id: '24a397f4-16dd-4dae-8b8f-5368c3a81fed',
                    response_type: 'code',  // Use authorization code flow for better security
                    response_mode: 'query',
                    scope: 'https://graph.microsoft.com/User.Read https://graph.microsoft.com/OnlineMeetings.ReadWrite https://graph.microsoft.com/Calendars.Read openid profile email',
                    redirect_uri: window.location.origin + '/html/auth-callback.html',
                    state: state,
                    prompt: 'select_account'  // Allow user to select account
                };

                // Add login hint if available
                if (loginHint) {
                    queryParams.login_hint = loginHint;
                }

                // Build the authorization endpoint URL
                const authorizeEndpoint = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/authorize?${toQueryString(queryParams)}`;

                console.log('AccureMD: Redirecting to OAuth endpoint:', authorizeEndpoint);
                console.log('AccureMD: Using tenant ID:', tenantId);
                console.log('AccureMD: Using login hint:', loginHint || 'none');

                // Redirect to Microsoft OAuth
                window.location.assign(authorizeEndpoint);

            } catch (error) {
                console.error('AccureMD: Failed to start authentication:', error);
                localStorage.setItem('accuremd.auth.error', JSON.stringify({
                    error: 'auth_start_failed',
                    error_description: 'Failed to start authentication: ' + error.message
                }));
                // Redirect to callback with error
                window.location.assign(window.location.origin + '/html/auth-callback.html');
            }
        }

        // Start authentication immediately
        startAuthentication();
    </script>
</body>
</html>
